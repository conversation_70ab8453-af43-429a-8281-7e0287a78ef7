@echo off
echo ===================================
echo DNS中继器实验报告测试脚本
echo ===================================
echo.

echo 1. 编译项目...
make clean
make all
if errorlevel 1 (
    echo 编译失败！
    pause
    exit /b 1
)
echo 编译成功！
echo.

echo 2. 启动DNS中继器（后台运行）...
start /b dnsrelay.exe -d
timeout /t 3 /nobreak >nul
echo DNS中继器已启动
echo.

echo 3. 基本功能测试...
echo 测试正常域名解析：
nslookup www.baidu.com 127.0.0.1
echo.

echo 测试本地配置域名：
nslookup bupt 127.0.0.1
echo.

echo 测试域名拦截功能：
nslookup www.163daohang.com.cn 127.0.0.1
echo.

echo 4. 缓存性能测试...
echo 第一次查询（建立缓存）：
nslookup www.google.com 127.0.0.1
echo.
echo 第二次查询（缓存命中）：
nslookup www.google.com 127.0.0.1
echo.

echo 5. 并发测试...
echo 启动10个并发查询...
for /l %%i in (1,1,10) do (
    start /b nslookup www.example%%i.com 127.0.0.1 >nul 2>&1
)
timeout /t 5 /nobreak >nul
echo 并发测试完成
echo.

echo 6. 查看日志文件...
if exist dnsrelay.log (
    echo 最近的日志记录：
    powershell "Get-Content dnsrelay.log | Select-Object -Last 10"
) else (
    echo 日志文件不存在
)
echo.

echo ===================================
echo 测试完成！请查看上述输出结果
echo 按任意键关闭DNS中继器并退出...
echo ===================================
pause >nul

echo 关闭DNS中继器...
taskkill /f /im dnsrelay.exe >nul 2>&1
echo 测试脚本执行完毕！
