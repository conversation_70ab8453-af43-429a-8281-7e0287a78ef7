{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "D:/software/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include"], "linkDirectories": ["D:/software/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0", "D:/software/Qt/Tools/mingw1310_64/lib/gcc", "D:/software/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/lib", "D:/software/Qt/Tools/mingw1310_64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "moldname", "mingwex", "kernel32", "pthread", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc", "moldname", "mingwex", "kernel32"]}, "path": "d:/software/Qt/Tools/mingw1310_64/bin/gcc.exe", "version": "13.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"implicit": {}, "path": "D:/software/Qt/Tools/mingw1310_64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}