#!/bin/bash

echo "==================================="
echo "DNS中继器实验报告测试脚本"
echo "==================================="
echo

echo "1. 编译项目..."
make clean
make all
if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi
echo "编译成功！"
echo

echo "2. 启动DNS中继器（后台运行）..."
sudo ./dnsrelay -d &
DNS_PID=$!
sleep 3
echo "DNS中继器已启动 (PID: $DNS_PID)"
echo

echo "3. 基本功能测试..."
echo "测试正常域名解析："
nslookup www.baidu.com 127.0.0.1
echo

echo "测试本地配置域名："
nslookup bupt 127.0.0.1
echo

echo "测试域名拦截功能："
nslookup www.163daohang.com.cn 127.0.0.1
echo

echo "4. 缓存性能测试..."
echo "第一次查询（建立缓存）："
time nslookup www.google.com 127.0.0.1 >/dev/null 2>&1
echo

echo "第二次查询（缓存命中）："
time nslookup www.google.com 127.0.0.1 >/dev/null 2>&1
echo

echo "5. 并发测试..."
echo "启动10个并发查询..."
for i in {1..10}; do
    nslookup www.example$i.com 127.0.0.1 >/dev/null 2>&1 &
done
wait
echo "并发测试完成"
echo

echo "6. 使用dig进行详细测试..."
if command -v dig >/dev/null 2>&1; then
    echo "dig测试结果："
    dig @127.0.0.1 www.bupt.edu.cn
else
    echo "dig命令不可用，跳过此测试"
fi
echo

echo "7. 查看日志文件..."
if [ -f "dnsrelay.log" ]; then
    echo "最近的日志记录："
    tail -10 dnsrelay.log
else
    echo "日志文件不存在"
fi
echo

echo "==================================="
echo "测试完成！请查看上述输出结果"
echo "按Enter键关闭DNS中继器并退出..."
echo "==================================="
read

echo "关闭DNS中继器..."
sudo kill $DNS_PID 2>/dev/null
echo "测试脚本执行完毕！"
