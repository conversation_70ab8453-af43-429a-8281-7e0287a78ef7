D:\software\Qt\Tools\CMake_64\bin\cmake.exe -E rm -f CMakeFiles\dnsrelay.dir/objects.a
D:\software\Qt\Tools\mingw1310_64\bin\ar.exe qc CMakeFiles\dnsrelay.dir/objects.a @CMakeFiles\dnsrelay.dir\objects1.rsp
d:\software\Qt\Tools\mingw1310_64\bin\gcc.exe -g -Wl,--whole-archive CMakeFiles\dnsrelay.dir/objects.a -Wl,--no-whole-archive -o dnsrelay.exe -Wl,--out-implib,libdnsrelay.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\dnsrelay.dir\linkLibs.rsp
